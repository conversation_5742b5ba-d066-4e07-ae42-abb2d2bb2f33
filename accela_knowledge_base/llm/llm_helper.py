"""
LLM Helper for strategic LLM usage
Only uses LLM where it genuinely adds value
"""

import json
from typing import Dict, List, Any, Optional
from ..core.config import Config
from ..core.exceptions import LLMError
from ..core.logging import LoggerMixin

# Optional LLM integration - only use if available and beneficial
try:
    from openai import OpenAI
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False


class LLMHelper(LoggerMixin):
    """Helper class for strategic LLM usage - only where it adds real value"""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = None
        self._available = False
        
        if LLM_AVAILABLE and config.llm_enabled:
            self.client = OpenAI(api_key=config.openai_api_key)
            self._available = True
            self.logger.info("LLM helper initialized successfully")
        else:
            self.logger.info("LLM not available or not configured")
    
    def is_available(self) -> bool:
        """Check if LLM is available"""
        return self._available
    
    def analyze_code_semantics(self, code_content: str, context: str = "") -> Dict[str, Any]:
        """
        Use LLM to understand code semantics - genuinely useful for code analysis
        
        Args:
            code_content: JavaScript code to analyze
            context: Additional context for analysis
            
        Returns:
            Dictionary with semantic analysis results
        """
        if not self._available or len(code_content) > self.config.max_code_length_for_llm:
            return {
                "semantic_analysis": "LLM not available or content too large", 
                "confidence": 0.0
            }
        
        prompt = self._build_code_analysis_prompt(code_content, context)

        response = self.client.chat.completions.create(
            model=self.config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_analysis,
            temperature=self.config.llm_temperature
        )

        result = json.loads(response.choices[0].message.content)
        result["confidence"] = 0.8

        self.logger.debug(f"Code semantic analysis completed with confidence {result['confidence']}")
        return result
    
    def generate_implementation_reasoning(self, implementations: List[Dict], query: str) -> str:
        """
        Use LLM to generate human-readable reasoning - valuable for explanations
        
        Args:
            implementations: List of implementation options
            query: Original query
            
        Returns:
            Human-readable reasoning string
        """
        if not self._available or len(implementations) == 0:
            return "LLM reasoning not available"
        
        prompt = self._build_reasoning_prompt(implementations, query)

        response = self.client.chat.completions.create(
            model=self.config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_reasoning,
            temperature=0.2
        )

        reasoning = response.choices[0].message.content.strip()
        self.logger.debug("Implementation reasoning generated successfully")
        return reasoning
    
    def _build_code_analysis_prompt(self, code_content: str, context: str) -> str:
        """Build prompt for code semantic analysis"""
        return f"""Analyze this Accela JavaScript code and provide semantic insights:

Context: {context}

Code:
{code_content[:1000]}...

Provide a JSON response with:
1. "purpose": What this script actually does (1-2 sentences)
2. "business_logic": Key business rules implemented
3. "integration_points": External systems or APIs used
4. "complexity_factors": What makes this code complex
5. "improvement_suggestions": Specific technical improvements

Keep response concise and technical."""
    
    def _build_reasoning_prompt(self, implementations: List[Dict], query: str) -> str:
        """Build prompt for implementation reasoning"""
        # Prepare concise implementation summaries
        impl_summaries = []
        for impl in implementations[:3]:  # Only top 3 to save tokens
            summary = f"County: {impl.get('county', 'Unknown')}, "
            summary += f"Score: {impl.get('score', 0):.2f}, "
            summary += f"Functions: {', '.join(impl.get('metadata', {}).get('functions', [])[:3])}"
            impl_summaries.append(summary)
        
        return f"""Given this query: "{query}"

And these top implementations:
{chr(10).join(f"{i+1}. {summary}" for i, summary in enumerate(impl_summaries))}

Provide a clear, technical explanation of why the top implementation is recommended. Focus on:
1. Technical merits
2. Functional completeness  
3. Implementation quality
4. Specific advantages over alternatives

Keep response under 100 words and technical."""
