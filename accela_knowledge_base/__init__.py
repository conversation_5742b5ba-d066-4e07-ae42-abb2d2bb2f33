"""
Accela Knowledge Base - ASK API
Natural language interface for querying Accela implementations across multiple counties
"""

__version__ = "2.0.0"
__author__ = "Accela Knowledge Base Team"
__description__ = "Natural language interface for Accela implementation queries"

from .core.config import Config
from .core.exceptions import AccelaKnowledgeBaseError
from .knowledge_graph.graph_builder import AccelaKnowledgeGraph

__all__ = [
    "Config",
    "AccelaKnowledgeBaseError",
    "AccelaKnowledgeGraph"
]
