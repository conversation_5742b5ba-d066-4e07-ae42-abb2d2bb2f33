"""
Metadata extractor for Accela scripts
Scans directories and extracts metadata from all scripts
"""

import json
from pathlib import Path
from typing import List, Dict

from ..core.config import Config
from ..core.models import ScriptMetadata
from ..core.logging import LoggerMixin
from .script_analyzer import ScriptAnalyzer


class MetadataExtractor(LoggerMixin):
    """Extracts metadata from all Accela scripts"""
    
    def __init__(self, config: Config):
        self.config = config
        self.script_analyzer = ScriptAnalyzer()
        self.src_dir = Path(config.src_directory)
    
    def extract_all(self) -> List[ScriptMetadata]:
        """
        Extract metadata from all Accela scripts
        
        Returns:
            List of ScriptMetadata objects
        """
        self.logger.info("Starting metadata extraction from all Accela scripts")
        
        if not self.src_dir.exists():
            self.logger.error(f"Source directory not found: {self.src_dir}")
            return []
        
        all_scripts = []
        
        # Scan each county directory
        for county_dir in self.src_dir.iterdir():
            if county_dir.is_dir():
                county_name = self._get_county_name(county_dir.name)
                self.logger.info(f"Processing county: {county_name}")
                
                county_scripts = self._scan_county_directory(county_dir, county_name)
                all_scripts.extend(county_scripts)
                
                self.logger.info(f"Found {len(county_scripts)} scripts in {county_name}")
        
        self.logger.info(f"Total scripts extracted: {len(all_scripts)}")
        
        # Save metadata to file
        self._save_metadata(all_scripts)
        
        return all_scripts
    
    def _get_county_name(self, dir_name: str) -> str:
        """Get county name from directory name"""
        return self.config.county_mappings.get(dir_name, dir_name.lower())
    
    def _scan_county_directory(self, county_dir: Path, county_name: str) -> List[ScriptMetadata]:
        """
        Scan a county directory for JavaScript files
        
        Args:
            county_dir: Path to county directory
            county_name: Name of the county
            
        Returns:
            List of ScriptMetadata objects for this county
        """
        scripts = []
        
        # Recursively find all .js files
        js_files = list(county_dir.rglob("*.js"))
        
        for js_file in js_files:
            try:
                script_metadata = self.script_analyzer.analyze_script(js_file, county_name)
                if script_metadata:
                    scripts.append(script_metadata)
                    
            except Exception as e:
                self.logger.warning(f"Failed to analyze {js_file}: {e}")
        
        return scripts
    
    def _save_metadata(self, scripts: List[ScriptMetadata]) -> None:
        """
        Save metadata to JSON file
        
        Args:
            scripts: List of ScriptMetadata objects
        """
        try:
            # Convert to dictionaries for JSON serialization
            scripts_data = []
            for script in scripts:
                script_dict = {
                    'file_path': script.file_path,
                    'county': script.county,
                    'script_type': script.script_type,
                    'naming_convention': {
                        'event_prefix': script.naming_convention.event_prefix,
                        'module': script.naming_convention.module,
                        'application_type': script.naming_convention.application_type,
                        'sub_type': script.naming_convention.sub_type,
                        'category': script.naming_convention.category,
                        'is_wildcard': script.naming_convention.is_wildcard,
                        'raw_filename': script.naming_convention.raw_filename
                    },
                    'functions': script.functions,
                    'dependencies': script.dependencies,
                    'last_modified': script.last_modified,
                    'complexity': script.complexity,
                    'documentation_quality': script.documentation_quality,
                    'file_hash': script.file_hash,
                    'content': script.content or '',  # Include content for analysis
                    'module': script.module,  # Legacy
                    'app_type': script.app_type  # Legacy
                }
                scripts_data.append(script_dict)
            
            # Save to file
            with open(self.config.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(scripts_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Metadata saved to {self.config.metadata_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save metadata: {e}")
            raise
    
    def load_metadata(self) -> List[Dict]:
        """
        Load metadata from JSON file
        
        Returns:
            List of metadata dictionaries
        """
        try:
            with open(self.config.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Metadata file not found: {self.config.metadata_file}")
            return []
        except Exception as e:
            self.logger.error(f"Failed to load metadata: {e}")
            return []
