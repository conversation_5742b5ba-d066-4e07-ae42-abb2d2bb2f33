"""
Agentic endpoints for multi-agent orchestration
"""

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel
from typing import Optional

from ...core.logging import get_logger
from ...core.dynamic_orchestrator import DynamicOrchestrator

router = APIRouter()
logger = get_logger("agentic")


class NaturalLanguageQueryRequest(BaseModel):
    query: str
    counties: Optional[str] = None  # Comma-separated county names


@router.post("/ask", response_class=PlainTextResponse)
async def natural_language_ask(request: NaturalLanguageQueryRequest, app_request: Request):
    """Simple natural language interface - just ask a question about Accela implementations with detailed code analysis"""

    # Parse counties if provided
    target_counties = None
    if request.counties:
        target_counties = [county.strip().lower().replace(' ', '_') for county in request.counties.split(',')]

    # Get the dynamic orchestrator from app state
    dynamic_orchestrator = app_request.app.state.dynamic_orchestrator

    # Process the query dynamically
    result = await dynamic_orchestrator.orchestrate(
        query=request.query,
        counties=target_counties
    )

    # Return the markdown response directly as plain text
    return result.markdown_response
