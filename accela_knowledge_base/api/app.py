"""
FastAPI application factory
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from ..core.config import Config
from ..core.logging import setup_logging, get_logger
from ..core.env_manager import EnvironmentManager
from .endpoints import agentic


def create_app(config: Config = None) -> FastAPI:
    """
    Create and configure FastAPI application
    
    Args:
        config: Configuration object
        
    Returns:
        Configured FastAPI application
    """
    
    if config is None:
        config = Config.from_env()

    # Setup environment
    env_manager = EnvironmentManager()
    env_manager.create_logs_directory()

    # Setup logging
    setup_logging()
    logger = get_logger("api")

    # Validate configuration
    try:
        config.validate()
        logger.info(f"Configuration validated: {config}")

        # Validate environment
        validation = env_manager.validate_environment()
        if not validation['valid']:
            logger.warning(f"Environment validation issues: {validation['errors']}")

    except ValueError as e:
        logger.error(f"Invalid configuration: {e}")
        raise
    
    # Create FastAPI app
    app = FastAPI(
        title="Accela Knowledge Base - ASK API",
        description="Natural language interface for Accela implementation queries",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware with environment-specific origins
    cors_origins = config.api_cors_origins if hasattr(config, 'api_cors_origins') else ["*"]
    if isinstance(cors_origins, str):
        cors_origins = [origin.strip() for origin in cors_origins.split(",")]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Store config in app state
    app.state.config = config
    
    # Include routers - only ASK endpoint
    app.include_router(agentic.router, prefix="/agentic", tags=["agentic"])
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize system on startup"""
        logger.info("Starting Accela Knowledge Base API")
        
        # Initialize knowledge graph and orchestrator (only what's needed for ASK endpoint)
        try:
            from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
            from ..core.dynamic_orchestrator import DynamicOrchestrator

            # Build knowledge graph once during startup
            knowledge_graph = AccelaKnowledgeGraph(config)
            knowledge_graph.build_from_metadata()

            # Initialize dynamic orchestrator (used by ASK endpoint)
            dynamic_orchestrator = DynamicOrchestrator(config)

            # Store in app state
            app.state.knowledge_graph = knowledge_graph
            app.state.dynamic_orchestrator = dynamic_orchestrator

            logger.info("System initialized successfully - ASK endpoint ready")
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            raise
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup on shutdown"""
        logger.info("Shutting down Accela Knowledge Base API")
    
    return app
