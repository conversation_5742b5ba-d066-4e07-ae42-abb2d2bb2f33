import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from .dynamic_analyzer import CountyAnalysisResult, ComparisonResult
from ..llm.llm_helper import LLMHelper


class DynamicResponseGenerator(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None

    def generate_response(self,
                         query_analysis: DynamicQueryAnalysis,
                         county_results: List[CountyAnalysisResult],
                         comparison_result: Optional[ComparisonResult] = None) -> str:
        return self._generate_with_llm(query_analysis, county_results, comparison_result)

    def _generate_with_llm(self, 
                          query_analysis: DynamicQueryAnalysis,
                          county_results: List[CountyAnalysisResult],
                          comparison_result: Optional[ComparisonResult]) -> str:
        """Generate response using LLM intelligence"""
        
        # Build comprehensive prompt
        prompt = self._build_response_prompt(query_analysis, county_results, comparison_result)

        response = self.llm_helper.client.chat.completions.create(
            model=self.config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_reasoning * 20,
            temperature=self.config.llm_temperature * 3
        )

        markdown_response = response.choices[0].message.content.strip()

        # Add metadata footer
        markdown_response += self._generate_metadata_footer(query_analysis, county_results)

        self.logger.info("LLM response generated successfully")
        return markdown_response
    
    def _build_response_prompt(self, 
                              query_analysis: DynamicQueryAnalysis,
                              county_results: List[CountyAnalysisResult],
                              comparison_result: Optional[ComparisonResult]) -> str:
        """Build comprehensive prompt for response generation"""
        
        # Prepare data for prompt
        county_data = []
        for result in county_results:
            county_data.append({
                'county': result.county,
                'findings': result.findings,
                'code_examples': result.code_examples[:self.config.max_search_results//4],
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'recommendations': result.recommendations
            })
        
        comparison_data = {}
        if comparison_result:
            comparison_data = {
                'similarities': comparison_result.similarities,
                'differences': comparison_result.differences[:5],  # Top 5 differences
                'best_practices': comparison_result.best_practices,
                'summary': comparison_result.summary
            }
        
        return f"""
You are an expert Accela implementation consultant. Create a comprehensive, professional markdown response to answer the user's query.

ORIGINAL QUERY: "{query_analysis.original_query}"

QUERY INTENT: {query_analysis.intent}

ANALYSIS TYPE: {query_analysis.analysis_type}

COUNTY ANALYSIS RESULTS:
{json.dumps(county_data, indent=2)}

COMPARISON RESULTS (if applicable):
{json.dumps(comparison_data, indent=2) if comparison_data else "No comparison performed"}

OUTPUT REQUIREMENTS:
- Format: Professional markdown
- Include code examples where relevant
- Detail level: {query_analysis.output_requirements.get('detail_level', 'high')}
- Include examples: {query_analysis.output_requirements.get('include_examples', True)}
- Include code: {query_analysis.output_requirements.get('include_code', True)}

Please create a comprehensive response that:

1. **Directly answers the user's question** with a clear, concise summary
2. **Provides detailed findings** for each county analyzed
3. **Includes relevant code examples** with script paths and explanations
4. **Highlights key differences** between counties (if comparison was performed)
5. **Identifies best practices** and recommendations
6. **Uses professional markdown formatting** with headers, bullet points, code blocks
7. **Focuses on actionable insights** that help with Accela implementation decisions

Structure your response with:
- Executive Summary
- Detailed Findings (by county)
- Code Examples (if relevant)
- Comparison Analysis (if multiple counties)
- Best Practices & Recommendations
- Next Steps

Use markdown code blocks for code examples with appropriate language tags (javascript, sql, etc.).
IMPORTANT: Always include the script path for every code example to enable cross-validation.
Make the response comprehensive but well-organized and easy to read.
"""
    


    def _generate_strategic_recommendations(self,
                                          query_analysis: DynamicQueryAnalysis,
                                          county_results: List[CountyAnalysisResult],
                                          comparison_result: Optional[ComparisonResult]) -> List[Dict[str, str]]:
        """Generate strategic recommendations for senior county officials"""

        recommendations = []

        if len(county_results) > 1:
            # Multi-county comparison recommendations
            best_county = max(county_results, key=lambda x: len([s for s in x.strengths if ':' in s]))

            recommendations.append({
                'title': 'Adopt Best Practice Patterns',
                'description': f"Consider implementing {self._format_county_name(best_county.county)}'s proven patterns for enhanced functionality.",
                'impact': 'Improved system reliability and reduced development time'
            })

            recommendations.append({
                'title': 'Standardize Implementation Approaches',
                'description': 'Establish common coding standards and patterns across jurisdictions for better maintainability.',
                'impact': 'Reduced training costs and improved code quality'
            })
        else:
            # Single county recommendations
            result = county_results[0] if county_results else None
            if result:
                capability_count = len([s for s in result.strengths if ':' in s])

                if capability_count >= 3:
                    recommendations.append({
                        'title': 'Leverage Existing Capabilities',
                        'description': f"Build upon the {capability_count} strong implementation areas already in place.",
                        'impact': 'Maximize ROI on existing Accela investments'
                    })
                else:
                    recommendations.append({
                        'title': 'Expand Implementation Scope',
                        'description': 'Consider implementing additional Accela automation patterns to improve efficiency.',
                        'impact': 'Reduced manual processing and improved citizen service'
                    })

        # Add query-specific recommendations
        if 'coding standards' in query_analysis.original_query.lower():
            recommendations.append({
                'title': 'Establish Code Review Process',
                'description': 'Implement peer review and automated testing for all Accela customizations.',
                'impact': 'Higher code quality and reduced production issues'
            })

        if 'best practice' in query_analysis.original_query.lower():
            recommendations.append({
                'title': 'Document Implementation Patterns',
                'description': 'Create internal documentation of proven patterns for future development.',
                'impact': 'Faster development cycles and knowledge retention'
            })

        return recommendations[:3]  # Top 3 strategic recommendations

    def _generate_direct_answer(self,
                               query_analysis: DynamicQueryAnalysis,
                               county_results: List[CountyAnalysisResult],
                               comparison_result: Optional[ComparisonResult]) -> str:
        """Generate direct answer based on query intent and results - from experienced Accela developer perspective"""

        if query_analysis.intent == "compare_implementations" and len(county_results) > 1:
            return self._generate_comparison_answer(query_analysis, county_results, comparison_result)
        elif query_analysis.intent == "find_best_practice":
            return self._generate_best_practice_answer(query_analysis, county_results)
        elif query_analysis.intent == "understand_workflow":
            return self._generate_workflow_answer(query_analysis, county_results)
        else:
            return self._generate_general_answer(query_analysis, county_results)

    def _generate_comparison_answer(self,
                                   query_analysis: DynamicQueryAnalysis,
                                   county_results: List[CountyAnalysisResult],
                                   comparison_result: Optional[ComparisonResult]) -> str:
        """Generate comparison answer from experienced Accela consultant perspective"""

        if not county_results:
            return "**No implementations found** for comparison in the analyzed counties."

        # Find the best implementation based on scripts analyzed and strengths
        best_county = max(county_results, key=lambda x: (
            x.metadata.get('scripts_analyzed', 0) * self.config.similarity_threshold * 2 +
            len(x.strengths) * self.config.similarity_threshold * 1.3
        ))

        county_names = [self._format_county_name(r.county) for r in county_results]

        answer_parts = []

        # Main recommendation
        if "coding standards" in query_analysis.original_query.lower():
            answer_parts.append(f"**{self._format_county_name(best_county.county)}** demonstrates superior Accela coding standards based on my analysis of {best_county.metadata.get('scripts_analyzed', 0)} scripts.")

            # Add specific reasoning
            key_strengths = [s for s in best_county.strengths if any(term in s.lower() for term in ['function', 'error', 'standard', 'pattern', 'quality'])]
            if key_strengths:
                answer_parts.append(f"**Key advantages:** {', '.join(key_strengths[:2])}")
        else:
            answer_parts.append(f"**{self._format_county_name(best_county.county)}** shows the strongest implementation among {', '.join(county_names)}.")

        # Add confidence qualifier - EVENT SCRIPTS ONLY
        scripts_analyzed = sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)
        if scripts_analyzed > 15:
            answer_parts.append(f"*High confidence based on {scripts_analyzed} event scripts analyzed across jurisdictions.*")
        else:
            answer_parts.append(f"*Moderate confidence based on {scripts_analyzed} event scripts analyzed.*")

        return " ".join(answer_parts)

    def _generate_best_practice_answer(self,
                                      query_analysis: DynamicQueryAnalysis,
                                      county_results: List[CountyAnalysisResult]) -> str:
        """Generate best practice answer"""

        if not county_results:
            return "**No best practices found** in the analyzed implementations."

        # Find county with most strengths and code examples
        best_county = max(county_results, key=lambda x: len(x.strengths) + len(x.code_examples))

        answer_parts = []
        answer_parts.append(f"**Best practice identified in {self._format_county_name(best_county.county)}:**")

        if best_county.strengths:
            top_strength = best_county.strengths[0]
            answer_parts.append(f"{top_strength}")

        if best_county.code_examples:
            answer_parts.append(f"*Demonstrated in {len(best_county.code_examples)} code examples.*")

        return " ".join(answer_parts)

    def _generate_workflow_answer(self,
                                 query_analysis: DynamicQueryAnalysis,
                                 county_results: List[CountyAnalysisResult]) -> str:
        """Generate workflow understanding answer"""

        if not county_results:
            return "**No workflow implementations found** in the analyzed counties."

        # Focus on county with most relevant findings
        best_county = max(county_results, key=lambda x: len(x.findings))

        answer_parts = []
        answer_parts.append(f"**Workflow pattern identified in {self._format_county_name(best_county.county)}:**")

        if best_county.findings:
            # Look for workflow-related findings
            workflow_findings = [f for f in best_county.findings.values() if isinstance(f, str) and any(term in f.lower() for term in ['workflow', 'process', 'step', 'approval'])]
            if workflow_findings:
                answer_parts.append(workflow_findings[0])

        return " ".join(answer_parts)

    def _generate_general_answer(self,
                                query_analysis: DynamicQueryAnalysis,
                                county_results: List[CountyAnalysisResult]) -> str:
        """Generate general answer for other query types"""

        if not county_results:
            return "**No relevant implementations found** for this query."

        total_scripts = sum(r.metadata.get('scripts_analyzed', 0) for r in county_results)
        county_names = [self._format_county_name(r.county) for r in county_results]

        if len(county_results) == 1:
            return f"**Found {total_scripts} relevant implementations** in {county_names[0]} for your query."
        else:
            return f"**Analyzed {total_scripts} implementations** across {len(county_results)} counties: {', '.join(county_names)}."

    def _format_county_name(self, county_code: str) -> str:
        """Format county code into a readable display name"""

        # County name mappings for better display
        county_display_names = {
            'asheville': 'Asheville',
            'santa_barbara': 'Santa Barbara County',
            'marin': 'Marin County',
            'san_mateo': 'San Mateo County',
            'alameda': 'Alameda County',
            'contra_costa': 'Contra Costa County',
            'sonoma': 'Sonoma County',
            'napa': 'Napa County',
            'solano': 'Solano County',
            'mendocino': 'Mendocino County',
            'dayton': 'Dayton',
            'leon': 'Leon County',
            'atlanta_chattanooga': 'Atlanta Chattanooga',
            'lancaster': 'Lancaster County',
            'cok': 'City of Kirkland',
            'ep_support': 'EP Support',
        }

        return county_display_names.get(county_code, county_code.title().replace('_', ' '))
    
    def _generate_metadata_footer(self, 
                                 query_analysis: DynamicQueryAnalysis,
                                 county_results: List[CountyAnalysisResult]) -> str:
        """Generate metadata footer for the response"""
        
        footer_parts = [
            "",
            "---",
            "",
            "### Analysis Metadata",
            "",
            f"**Query Type:** {query_analysis.analysis_type}",
            f"**Counties Analyzed:** {', '.join([r.county for r in county_results])}",
            f"**Analysis Confidence:** {query_analysis.confidence:.1%}",
            f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "*Generated by Accela Knowledge Base - Dynamic AI Analysis System*"
        ]
        
        return "\n".join(footer_parts)
