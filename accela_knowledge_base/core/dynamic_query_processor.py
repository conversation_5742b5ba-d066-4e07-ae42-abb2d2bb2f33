import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from ..llm.llm_helper import <PERSON><PERSON>Helper


@dataclass
class DynamicQueryAnalysis:
    original_query: str
    intent: str
    entities: Dict[str, List[str]]
    comparison_requested: bool
    analysis_type: str
    specific_aspects: List[str]
    output_requirements: Dict[str, Any]
    confidence: float


class DynamicQueryProcessor(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.available_counties = self._load_available_counties()
        self.available_concepts = self._load_available_concepts()
    
    def process_query(self, query: str) -> DynamicQueryAnalysis:
        if self.llm_helper:
            return self._process_with_llm(query)
        else:
            return self._process_with_fallback(query)
    
    def _process_with_llm(self, query: str) -> DynamicQueryAnalysis:
        try:
            prompt = self._build_query_analysis_prompt(query)

            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.llm_max_tokens_analysis,
                temperature=self.config.llm_temperature
            )

            llm_analysis = self._parse_llm_analysis(response.choices[0].message.content)

            return DynamicQueryAnalysis(
                original_query=query,
                intent=llm_analysis.get('intent', 'general_inquiry'),
                entities=llm_analysis.get('entities', {}),
                comparison_requested=llm_analysis.get('comparison_requested', False),
                analysis_type=llm_analysis.get('analysis_type', 'general_analysis'),
                specific_aspects=llm_analysis.get('specific_aspects', []),
                output_requirements=llm_analysis.get('output_requirements', {}),
                confidence=llm_analysis.get('confidence', self.config.similarity_threshold)
            )

        except Exception as e:
            self.logger.error(f"LLM query processing failed: {e}")
            return self._process_with_fallback(query)
    
    def _build_query_analysis_prompt(self, query: str) -> str:
        return f"""Analyze this Accela implementation query and respond with JSON:

USER QUERY: "{query}"

AVAILABLE COUNTIES: {', '.join(self.available_counties)}
AVAILABLE CONCEPTS: {', '.join(self.available_concepts)}

Respond with JSON containing:
1. "intent": What user wants ("compare_implementations", "find_best_practice", "understand_workflow", "get_code_examples")
2. "entities": {{"counties": [exact county names from list], "technical_concepts": [relevant concepts], "specific_terms": [technical terms]}}
3. "comparison_requested": true/false
4. "analysis_type": "code_analysis", "workflow_comparison", "configuration_analysis", "best_practices", "troubleshooting", "feature_exploration", or "general_inquiry"
5. "specific_aspects": [things to analyze]
6. "output_requirements": {{"format": "markdown", "include_code": true/false, "include_examples": true/false, "detail_level": "high/medium/low"}}
7. "confidence": 0.0-1.0

Use exact county names only. Extract all relevant technical concepts."""
    
    def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        try:
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                parsed_data = json.loads(json_str)

                if 'entities' in parsed_data and 'counties' in parsed_data['entities']:
                    entities = parsed_data['entities']
                    valid_counties = [
                        county.strip() for county in entities['counties']
                        if isinstance(county, str) and county.strip() and county.lower() != "string"
                    ]
                    entities['counties'] = valid_counties

                return parsed_data
            else:
                return {}

        except json.JSONDecodeError:
            return {}
    
    def _process_with_fallback(self, query: str) -> DynamicQueryAnalysis:
        """Fallback processing when LLM is not available"""
        
        query_lower = query.lower()
        
        # Basic entity extraction
        entities = {
            'counties': self._extract_counties_fallback(query_lower),
            'technical_concepts': self._extract_concepts_fallback(query_lower),
            'specific_terms': self._extract_terms_fallback(query_lower)
        }
        
        # Basic intent detection
        intent = self._detect_intent_fallback(query_lower)
        
        # Basic analysis type detection
        analysis_type = self._detect_analysis_type_fallback(query_lower)
        
        # Check for comparison
        comparison_requested = any(word in query_lower for word in 
                                ['compare', 'difference', 'vs', 'versus', 'between'])
        
        return DynamicQueryAnalysis(
            original_query=query,
            intent=intent,
            entities=entities,
            comparison_requested=comparison_requested,
            analysis_type=analysis_type,
            specific_aspects=self._extract_aspects_fallback(query_lower),
            output_requirements={
                'format': 'markdown',
                'include_code': True,
                'include_examples': True,
                'detail_level': 'high'
            },
            confidence=self.config.similarity_threshold
        )
    
    def _load_available_counties(self) -> List[str]:
        try:
            from ..data.metadata_extractor import MetadataExtractor
            extractor = MetadataExtractor(self.config)
            return extractor.get_available_counties()
        except:
            return ['asheville', 'atlanta_chattanooga', 'leon', 'cok', 'ep_support',
                   'dayton', 'solano', 'lancaster', 'santa_barbara']

    def _load_available_concepts(self) -> List[str]:
        return [
            'permits', 'inspections', 'licenses', 'planning', 'code_enforcement',
            'workflows', 'events', 'fees', 'notifications', 'reports',
            'applications', 'reviews', 'approvals', 'conditions', 'documents',
            'coding_standards', 'best_practices', 'code_quality', 'standards',
            'implementation', 'patterns', 'conventions', 'architecture',
            'error_handling', 'logging', 'validation', 'security', 'performance'
        ]
    
    def _extract_counties_fallback(self, query: str) -> List[str]:
        return [county for county in self.available_counties
                if county.replace('_', ' ') in query or county in query]

    def _extract_concepts_fallback(self, query: str) -> List[str]:
        return [concept for concept in self.available_concepts if concept in query]

    def _extract_terms_fallback(self, query: str) -> List[str]:
        terms = []
        terms.extend(re.findall(r'\b\w+\(\)', query))
        terms.extend(re.findall(r'\b[A-Z][a-zA-Z]*\.[a-zA-Z]+\b', query))
        return terms
    
    def _detect_intent_fallback(self, query: str) -> str:
        intent_map = {
            ('compare', 'difference', 'vs'): 'compare_implementations',
            ('best', 'optimal', 'recommend'): 'find_best_practice',
            ('how', 'workflow', 'process'): 'understand_workflow',
            ('code', 'function', 'implementation'): 'get_code_examples'
        }

        for keywords, intent in intent_map.items():
            if any(word in query for word in keywords):
                return intent
        return 'general_inquiry'

    def _detect_analysis_type_fallback(self, query: str) -> str:
        type_map = {
            ('code', 'function', 'script'): 'code_analysis',
            ('workflow', 'process', 'business'): 'workflow_comparison',
            ('config', 'setting', 'setup'): 'configuration_analysis',
            ('best', 'practice', 'optimal'): 'best_practices'
        }

        for keywords, analysis_type in type_map.items():
            if any(word in query for word in keywords):
                return analysis_type
        return 'general_inquiry'

    def _extract_aspects_fallback(self, query: str) -> List[str]:
        aspect_map = {
            'fee': ['fee_calculation', 'fee_structure', 'payment_processing'],
            'email': ['email_templates', 'notification_triggers', 'recipient_logic'],
            'workflow': ['approval_steps', 'routing_logic', 'status_transitions'],
            'inspection': ['scheduling', 'results_processing', 'follow_up_actions']
        }

        aspects = []
        for keyword, aspect_list in aspect_map.items():
            if keyword in query:
                aspects.extend(aspect_list)

        return aspects if aspects else ['general_implementation']
