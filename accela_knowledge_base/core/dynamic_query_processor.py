import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from ..llm.llm_helper import <PERSON><PERSON><PERSON>elper


@dataclass
class DynamicQueryAnalysis:
    original_query: str
    intent: str
    entities: Dict[str, List[str]]
    comparison_requested: bool
    analysis_type: str
    specific_aspects: List[str]
    output_requirements: Dict[str, Any]
    confidence: float


class DynamicQueryProcessor(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.available_counties = self._load_available_counties()
        self.available_concepts = self._load_available_concepts()
    
    def process_query(self, query: str) -> DynamicQueryAnalysis:
        return self._process_with_llm(query)
    
    def _process_with_llm(self, query: str) -> DynamicQueryAnalysis:
        prompt = self._build_query_analysis_prompt(query)

        response = self.llm_helper.client.chat.completions.create(
            model=self.config.llm_model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=self.config.llm_max_tokens_analysis,
            temperature=self.config.llm_temperature
        )

        llm_analysis = self._parse_llm_analysis(response.choices[0].message.content)

        return DynamicQueryAnalysis(
            original_query=query,
            intent=llm_analysis.get('intent', 'general_inquiry'),
            entities=llm_analysis.get('entities', {}),
            comparison_requested=llm_analysis.get('comparison_requested', False),
            analysis_type=llm_analysis.get('analysis_type', 'general_analysis'),
            specific_aspects=llm_analysis.get('specific_aspects', []),
            output_requirements=llm_analysis.get('output_requirements', {}),
            confidence=llm_analysis.get('confidence', self.config.similarity_threshold)
        )
    
    def _build_query_analysis_prompt(self, query: str) -> str:
        return f"""Analyze this Accela implementation query and respond with JSON:

USER QUERY: "{query}"

AVAILABLE COUNTIES: {', '.join(self.available_counties)}
AVAILABLE CONCEPTS: {', '.join(self.available_concepts)}

Respond with JSON containing:
1. "intent": What user wants ("compare_implementations", "find_best_practice", "understand_workflow", "get_code_examples")
2. "entities": {{"counties": [exact county names from list], "technical_concepts": [relevant concepts], "specific_terms": [technical terms]}}
3. "comparison_requested": true/false
4. "analysis_type": "code_analysis", "workflow_comparison", "configuration_analysis", "best_practices", "troubleshooting", "feature_exploration", or "general_inquiry"
5. "specific_aspects": [things to analyze]
6. "output_requirements": {{"format": "markdown", "include_code": true/false, "include_examples": true/false, "detail_level": "high/medium/low"}}
7. "confidence": 0.0-1.0

Use exact county names only. Extract all relevant technical concepts."""
    
    def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            parsed_data = json.loads(json_str)

            if 'entities' in parsed_data and 'counties' in parsed_data['entities']:
                entities = parsed_data['entities']
                valid_counties = [
                    county.strip() for county in entities['counties']
                    if isinstance(county, str) and county.strip() and county.lower() != "string"
                ]
                entities['counties'] = valid_counties

            return parsed_data
        else:
            return {}

    
    def _load_available_counties(self) -> List[str]:
        from ..data.metadata_extractor import MetadataExtractor
        extractor = MetadataExtractor(self.config)
        return extractor.get_available_counties()

    def _load_available_concepts(self) -> List[str]:
        return [
            'permits', 'inspections', 'licenses', 'planning', 'code_enforcement',
            'workflows', 'events', 'fees', 'notifications', 'reports',
            'applications', 'reviews', 'approvals', 'conditions', 'documents',
            'coding_standards', 'best_practices', 'code_quality', 'standards',
            'implementation', 'patterns', 'conventions', 'architecture',
            'error_handling', 'logging', 'validation', 'security', 'performance'
        ]

