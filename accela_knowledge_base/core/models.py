"""
Data models for Accela Knowledge Base
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any


@dataclass
class AccelaNamingConvention:
    """Parsed Accela script naming convention"""
    event_prefix: Optional[str] = None      # aaaa (e.g., ASA, WTUA, CRCA)
    module: Optional[str] = None            # b (e.g., Licenses, Permits, Planning)
    application_type: Optional[str] = None  # c (e.g., Building, Business, Case)
    sub_type: Optional[str] = None          # d (e.g., New, Amendment, Express)
    category: Optional[str] = None          # e (e.g., Application, Renewal, License)
    is_wildcard: bool = False               # True if uses ~ wildcards
    raw_filename: str = ""                  # Original filename


@dataclass
class ScriptMetadata:
    """Enhanced metadata structure for Accela scripts with naming convention"""
    file_path: str
    county: str
    script_type: str  # event, batch, interface, set, pageflow, expression
    naming_convention: AccelaNamingConvention = field(default_factory=AccelaNamingConvention)
    functions: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    last_modified: Optional[str] = None
    complexity: str = "medium"  # low, medium, high
    documentation_quality: str = "poor"  # poor, good, excellent
    file_hash: Optional[str] = None
    content: Optional[str] = None
    # Legacy fields for backward compatibility
    module: Optional[str] = None
    app_type: Optional[str] = None


# Legacy orchestration models removed - only ASK endpoint uses dynamic orchestrator


@dataclass
class KnowledgeNode:
    """Represents a node in the knowledge graph"""
    id: str
    type: str  # county, function, script_type, module, pattern
    properties: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class KnowledgeEdge:
    """Represents an edge in the knowledge graph"""
    source: str
    target: str
    relationship: str  # implements, uses, similar_to, depends_on, best_practice
    weight: float = 1.0
    properties: Dict[str, Any] = field(default_factory=dict)
