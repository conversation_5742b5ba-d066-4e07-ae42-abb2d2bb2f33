"""
Dynamic Orchestrator for Accela Knowledge Base
Coordinates dynamic query processing, analysis, and response generation
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryProcessor, DynamicQueryAnalysis
from .dynamic_analyzer import DynamicCountyAnalyzer, CountyAnalysisResult, ComparisonResult
from .dynamic_response_generator import DynamicResponseGenerator


class DynamicOrchestrationResult:
    def __init__(self,
                 request_id: str,
                 original_query: str,
                 query_analysis: DynamicQueryAnalysis,
                 county_results: List[CountyAnalysisResult],
                 comparison_result: Optional[ComparisonResult],
                 markdown_response: str,
                 processing_time: float):
        self.request_id = request_id
        self.original_query = original_query
        self.query_analysis = query_analysis
        self.county_results = county_results
        self.comparison_result = comparison_result
        self.markdown_response = markdown_response
        self.processing_time = processing_time
        self.confidence = query_analysis.confidence


class DynamicOrchestrator(LoggerMixin):
    def __init__(self, config: Config):
        self.config = config
        self.query_processor = DynamicQueryProcessor(config)
        self.analyzer = DynamicCountyAnalyzer(config)
        self.response_generator = DynamicResponseGenerator(config)
        self.logger.info("Dynamic orchestrator initialized")
    
    async def orchestrate(self, query: str, counties: Optional[List[str]] = None) -> DynamicOrchestrationResult:
        request_id = str(uuid.uuid4())
        start_time = datetime.now()

        try:
            query_analysis = self.query_processor.process_query(query)

            if counties:
                query_analysis.entities['counties'] = counties

            county_results = self.analyzer.analyze_counties(query_analysis)

            comparison_result = None
            if len(county_results) > 1:
                comparison_result = self.analyzer.compare_counties(query_analysis, county_results)

            markdown_response = self.response_generator.generate_response(
                query_analysis, county_results, comparison_result
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            return DynamicOrchestrationResult(
                request_id=request_id,
                original_query=query,
                query_analysis=query_analysis,
                county_results=county_results,
                comparison_result=comparison_result,
                markdown_response=markdown_response,
                processing_time=processing_time
            )

        except Exception as e:
            self.logger.error(f"Dynamic orchestration failed: {e}")
            raise
    
    def get_status(self) -> Dict[str, Any]:
        return {
            "status": "ready",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "query_processor": "ready",
                "analyzer": "ready",
                "response_generator": "ready"
            },
            "llm_enabled": self.config.llm_enabled
        }
