import os
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryAnalysis
from ..llm.llm_helper import LLMHelper
from ..data.metadata_extractor import MetadataExtractor


@dataclass
class CountyAnalysisResult:
    county: str
    analysis_type: str
    findings: Dict[str, Any]
    code_examples: List[Dict[str, str]]
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class ComparisonResult:
    counties: List[str]
    analysis_type: str
    comparison_matrix: Dict[str, Dict[str, Any]]
    similarities: List[str]
    differences: List[Dict[str, Any]]
    best_practices: List[Dict[str, Any]]
    recommendations: List[str]
    summary: str


class DynamicCountyAnalyzer(LoggerMixin):
    
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        self.metadata_extractor = MetadataExtractor(config)
        
        # Load county metadata
        self.county_metadata = self._load_county_metadata()
        
        self.logger.info("Dynamic county analyzer initialized")
    
    def analyze_counties(self, query_analysis: DynamicQueryAnalysis) -> List[CountyAnalysisResult]:
        """
        Analyze counties based on dynamic query analysis
        
        Args:
            query_analysis: Result from DynamicQueryProcessor
            
        Returns:
            List of CountyAnalysisResult for each relevant county
        """
        
        self.logger.info(f"Analyzing counties for: {query_analysis.intent}")
        
        # Determine which counties to analyze
        target_counties = self._determine_target_counties(query_analysis)
        
        results = []
        for county in target_counties:
            try:
                result = self._analyze_single_county(county, query_analysis)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to analyze {county}: {e}")
        
        return results
    
    def compare_counties(self, query_analysis: DynamicQueryAnalysis, 
                        county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """
        Compare multiple counties based on analysis results
        
        Args:
            query_analysis: Original query analysis
            county_results: Individual county analysis results
            
        Returns:
            ComparisonResult with detailed comparison
        """
        
        if len(county_results) < 2:
            self.logger.warning("Need at least 2 counties for comparison")
            return self._create_empty_comparison(query_analysis)
        
        self.logger.info(f"Comparing {len(county_results)} counties")
        
        # Always use knowledge base strictly - no LLM manipulation
        return self._compare_with_knowledge_base(query_analysis, county_results)
    
    def _analyze_single_county(self, county: str, 
                              query_analysis: DynamicQueryAnalysis) -> Optional[CountyAnalysisResult]:
        """Analyze a single county's implementation"""
        
        # Get county metadata and scripts
        county_data = self.county_metadata.get(county, {})
        if not county_data:
            self.logger.warning(f"No data found for county: {county}")
            return None
        
        # Get relevant scripts based on query analysis
        relevant_scripts = self._find_relevant_scripts(county_data, query_analysis)
        
        # Always use knowledge base strictly - no LLM manipulation of code
        return self._analyze_with_knowledge_base(county, relevant_scripts, query_analysis)
    
    def _analyze_with_llm(self, county: str, scripts: List[Dict], 
                         query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        """Analyze county using LLM intelligence"""
        
        try:
            # Build analysis prompt
            prompt = self._build_analysis_prompt(county, scripts, query_analysis)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.llm_max_tokens_reasoning * 10,
                temperature=self.config.llm_temperature * 2
            )
            
            # Parse LLM analysis
            llm_result = self._parse_llm_county_analysis(response.choices[0].message.content)
            
            return CountyAnalysisResult(
                county=county,
                analysis_type=query_analysis.analysis_type,
                findings=llm_result.get('findings', {}),
                code_examples=llm_result.get('code_examples', []),
                strengths=llm_result.get('strengths', []),
                weaknesses=llm_result.get('weaknesses', []),
                recommendations=llm_result.get('recommendations', []),
                confidence=llm_result.get('confidence', self.config.similarity_threshold),
                metadata={'scripts_analyzed': len(scripts)}
            )
            
        except Exception as e:
            self.logger.error(f"LLM analysis failed for {county}: {e}")
            return self._analyze_with_fallback(county, scripts, query_analysis)
    
    def _build_analysis_prompt(self, county: str, scripts: List[Dict], 
                              query_analysis: DynamicQueryAnalysis) -> str:
        """Build comprehensive analysis prompt for LLM"""
        
        # Prepare script information
        script_info = []
        for script in scripts[:5]:  # Limit to top 5 most relevant
            script_info.append({
                'path': script.get('file_path', ''),
                'functions': script.get('functions', [])[:10],  # Top 10 functions
                'content_preview': script.get('content', '')[:1000]  # First 1000 chars
            })
        
        return f"""
You are a senior Accela implementation consultant with 15+ years of experience across 100+ US jurisdictions. You've personally led implementations for counties ranging from 50K to 2M+ population, specializing in EMSE scripting, workflow optimization, and code quality assurance.

Your expertise includes:
- Advanced EMSE JavaScript patterns and anti-patterns
- Performance optimization for high-volume permitting systems
- Code review standards used by top-tier Accela implementations
- Municipal workflow best practices across diverse jurisdictions
- Integration patterns with GIS, financial, and third-party systems

Analyze {county.upper()}'s implementation as their trusted Accela consultant.

ORIGINAL QUERY: "{query_analysis.original_query}"

ANALYSIS TYPE: {query_analysis.analysis_type}

SPECIFIC ASPECTS TO ANALYZE: {', '.join(query_analysis.specific_aspects)}

COUNTY: {county}

RELEVANT SCRIPTS:
{json.dumps(script_info, indent=2)}

Please provide a comprehensive analysis in JSON format with:

1. "findings": {{
   "key_patterns": [list of important patterns found],
   "implementation_approach": "description of how this county implements the requested functionality",
   "technical_details": [specific technical findings],
   "business_logic": [business process insights]
}}

2. "code_examples": [
   {{
     "description": "what this code does",
     "code": "actual code snippet",
     "file": "source file path - ALWAYS include this for cross-validation",
     "significance": "why this is important"
   }}
]

3. "strengths": [list of what this county does well]

4. "weaknesses": [list of areas for improvement]

5. "recommendations": [specific actionable recommendations]

6. "confidence": 0.0-1.0 confidence in this analysis

Focus specifically on: {', '.join(query_analysis.specific_aspects)}

Respond ONLY with valid JSON.
"""
    
    def _analyze_with_knowledge_base(self, county: str, scripts: List[Dict],
                                    query_analysis: DynamicQueryAnalysis) -> CountyAnalysisResult:
        """Knowledge base only analysis - shows actual code without LLM manipulation"""
        
        findings = {}
        code_examples = []
        strengths = []
        weaknesses = []
        recommendations = []
        
        # Extract actual code examples based on query concepts
        query_concepts = query_analysis.entities.get('technical_concepts', [])
        query_terms = query_analysis.entities.get('specific_terms', [])

        # Combine all search terms
        search_terms = query_concepts + query_terms + [query_analysis.analysis_type]

        for script in scripts:  # Analyze ALL relevant scripts for comprehensive coverage
            content = script.get('content', '')
            file_path = script.get('file_path', 'unknown')

            if not content:
                continue

            # Find relevant code sections based on search terms
            relevant_sections = self._extract_relevant_code_sections(content, search_terms, file_path)
            code_examples.extend(relevant_sections)

            # Professional implementation analysis based on actual content
            functions = script.get('functions', [])
            content_lower = content.lower()

            # Analyze implementation patterns
            if 'addcontactscondition' in content_lower or 'editcapcontactattribute' in content_lower:
                if 'addcontactscondition' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Contact Management: Implements standardized contact validation and attribute management")

            if 'copyaddresses' in content_lower or 'copycontacts' in content_lower:
                if 'copyaddresses' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Data Replication: Automated address and contact copying between records")

            if 'createrecord' in content_lower or 'createchild' in content_lower:
                if 'createrecord' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Record Creation: Automated child record generation with proper linking")

            # Error handling analysis
            if ('try' in content_lower and 'catch' in content_lower) or 'error' in content_lower:
                if 'Error Handling' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Error Handling: Implements exception handling and error logging")

            # Workflow analysis
            if 'workflow' in content_lower or 'task' in content_lower:
                if 'Workflow' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Workflow Integration: Automated task and workflow management")

            # Fee calculation
            if 'fee' in content_lower or 'invoice' in content_lower:
                if 'Fee' not in [s.split(':')[0] for s in strengths]:
                    strengths.append("Fee Management: Automated fee calculation and invoice generation")

            # Identify weaknesses based on missing patterns
            if len(functions) < 2:
                weaknesses.append("Limited functional scope - may need additional business logic")

            if 'try' not in content_lower and 'error' not in content_lower and len(content) > 500:
                weaknesses.append("Missing comprehensive error handling for complex operations")
        
        # Basic findings
        findings = {
            'key_patterns': [f"Found {len(scripts)} relevant scripts"],
            'implementation_approach': f"{county} uses standard Accela patterns",
            'technical_details': [f"Analyzed {len(scripts)} scripts"],
            'business_logic': ["Standard government workflow patterns"]
        }
        
        # Basic recommendations
        recommendations = [
            f"Review {county}'s implementation for best practices",
            "Consider code optimization opportunities",
            "Ensure comprehensive testing"
        ]
        
        return CountyAnalysisResult(
            county=county,
            analysis_type=query_analysis.analysis_type,
            findings=findings,
            code_examples=code_examples,
            strengths=strengths,
            weaknesses=weaknesses,
            recommendations=recommendations,
            confidence=0.6,
            metadata={'scripts_analyzed': len(scripts)}
        )
    
    def _compare_with_llm(self, query_analysis: DynamicQueryAnalysis, 
                         county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """Compare counties using LLM intelligence"""
        
        try:
            # Build comparison prompt
            prompt = self._build_comparison_prompt(query_analysis, county_results)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2500,
                temperature=0.2
            )
            
            # Parse LLM comparison
            llm_comparison = self._parse_llm_comparison(response.choices[0].message.content)
            
            return ComparisonResult(
                counties=[r.county for r in county_results],
                analysis_type=query_analysis.analysis_type,
                comparison_matrix=llm_comparison.get('comparison_matrix', {}),
                similarities=llm_comparison.get('similarities', []),
                differences=llm_comparison.get('differences', []),
                best_practices=llm_comparison.get('best_practices', []),
                recommendations=llm_comparison.get('recommendations', []),
                summary=llm_comparison.get('summary', '')
            )
            
        except Exception as e:
            self.logger.error(f"LLM comparison failed: {e}")
            return self._compare_with_fallback(query_analysis, county_results)
    
    def _build_comparison_prompt(self, query_analysis: DynamicQueryAnalysis, 
                                county_results: List[CountyAnalysisResult]) -> str:
        """Build comparison prompt for LLM"""
        
        # Prepare county data for comparison
        county_data = []
        for result in county_results:
            county_data.append({
                'county': result.county,
                'findings': result.findings,
                'strengths': result.strengths,
                'weaknesses': result.weaknesses,
                'code_examples': [ex['description'] for ex in result.code_examples]
            })
        
        return f"""
You are a senior Accela implementation consultant conducting a comparative analysis for a client considering implementation approaches. Draw from your 15+ years of experience across 100+ jurisdictions to provide expert insights.

ORIGINAL QUERY: "{query_analysis.original_query}"

ANALYSIS TYPE: {query_analysis.analysis_type}

COUNTY ANALYSIS RESULTS:
{json.dumps(county_data, indent=2)}

Please provide a comprehensive comparison in JSON format with:

1. "comparison_matrix": {{
   "county1": {{
     "approach": "description",
     "complexity": "high/medium/low",
     "effectiveness": "high/medium/low",
     "maintainability": "high/medium/low"
   }},
   "county2": {{ ... }}
}}

2. "similarities": [list of common approaches/patterns across counties]

3. "differences": [
   {{
     "aspect": "what differs",
     "county1_approach": "how county1 does it",
     "county2_approach": "how county2 does it",
     "impact": "significance of this difference"
   }}
]

4. "best_practices": [
   {{
     "practice": "description of best practice",
     "county": "which county demonstrates this",
     "why": "why this is a best practice"
   }}
]

5. "recommendations": [actionable recommendations based on comparison]

6. "summary": "concise summary of key findings and recommendations"

Respond ONLY with valid JSON.
"""

    def _compare_with_fallback(self, query_analysis: DynamicQueryAnalysis,
                              county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """Fallback comparison when LLM is not available"""

        counties = [r.county for r in county_results]

        # Basic comparison matrix
        comparison_matrix = {}
        for result in county_results:
            comparison_matrix[result.county] = {
                'approach': f"Standard {result.analysis_type}",
                'complexity': 'medium',
                'effectiveness': 'medium',
                'maintainability': 'medium'
            }

        # Find similarities
        similarities = []
        all_strengths = [strength for result in county_results for strength in result.strengths]
        common_strengths = set()
        for strength in all_strengths:
            if all_strengths.count(strength) > 1:
                common_strengths.add(strength)
        similarities.extend(list(common_strengths))

        # Find differences
        differences = []
        for i, result1 in enumerate(county_results):
            for j, result2 in enumerate(county_results[i+1:], i+1):
                unique_strengths1 = set(result1.strengths) - set(result2.strengths)
                unique_strengths2 = set(result2.strengths) - set(result1.strengths)

                if unique_strengths1 or unique_strengths2:
                    differences.append({
                        'aspect': 'implementation_strengths',
                        'county1_approach': f"{result1.county}: {', '.join(unique_strengths1)}",
                        'county2_approach': f"{result2.county}: {', '.join(unique_strengths2)}",
                        'impact': 'Different implementation focuses'
                    })

        # Basic best practices
        best_practices = []
        for result in county_results:
            if result.confidence > self.config.similarity_threshold * 2:
                best_practices.append({
                    'practice': f"High-confidence implementation in {result.county}",
                    'county': result.county,
                    'why': f"Analysis confidence: {result.confidence:.2f}"
                })

        # Basic recommendations
        recommendations = [
            "Compare implementation approaches across counties",
            "Identify and adopt best practices from high-performing counties",
            "Consider standardizing common patterns"
        ]

        summary = f"Compared {len(counties)} counties for {query_analysis.analysis_type}. Found {len(similarities)} common patterns and {len(differences)} key differences."

        return ComparisonResult(
            counties=counties,
            analysis_type=query_analysis.analysis_type,
            comparison_matrix=comparison_matrix,
            similarities=similarities,
            differences=differences,
            best_practices=best_practices,
            recommendations=recommendations,
            summary=summary
        )

    def _determine_target_counties(self, query_analysis: DynamicQueryAnalysis) -> List[str]:
        """Determine which counties to analyze based on query"""

        # If specific counties mentioned, use those
        counties_from_entities = query_analysis.entities.get('counties', [])

        # Filter out invalid county names (like "string" or empty values)
        valid_counties = []
        for county in counties_from_entities:
            if isinstance(county, str) and county.strip() and county != "string":
                # Check if county exists in our metadata
                if county in self.county_metadata:
                    valid_counties.append(county)
                else:
                    # Try to find a matching county name
                    county_lower = county.lower().replace(' ', '_')
                    for available_county in self.county_metadata.keys():
                        if available_county.lower() == county_lower:
                            valid_counties.append(available_county)
                            break

        if valid_counties:
            self.logger.info(f"Using specific counties: {valid_counties}")
            return valid_counties

        # For comparison queries without specific counties, use a representative sample
        if query_analysis.comparison_requested or 'compare' in query_analysis.intent.lower():
            # Use a diverse set of counties for comparison
            sample_counties = ['asheville', 'santa_barbara', 'marin', 'dayton', 'leon']
            available_sample = [c for c in sample_counties if c in self.county_metadata]
            if available_sample:
                self.logger.info(f"Using sample counties for comparison: {available_sample}")
                return available_sample[:self.config.max_search_results//7]

        # Otherwise, return all available counties (but limit for performance)
        all_counties = list(self.county_metadata.keys())
        if len(all_counties) > 5:
            self.logger.info(f"Limiting analysis to first 5 counties: {all_counties[:5]}")
            return all_counties[:5]

        self.logger.info(f"Using all available counties: {all_counties}")
        return all_counties

    def _find_relevant_scripts(self, county_data: Dict,
                              query_analysis: DynamicQueryAnalysis) -> List[Dict]:
        """Find scripts relevant to the query analysis"""

        scripts = county_data.get('scripts', [])
        relevant_scripts = []

        # Score scripts based on relevance to query
        for script in scripts:
            score = self._calculate_script_relevance(script, query_analysis)
            if score > 0:
                script['relevance_score'] = score
                relevant_scripts.append(script)

        # Sort by relevance and return ALL relevant scripts (don't limit to 10)
        relevant_scripts.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        # Only filter out completely irrelevant scripts (score = 0)
        filtered_scripts = [s for s in relevant_scripts if s.get('relevance_score', 0) > 0]

        self.logger.info(f"Found {len(filtered_scripts)} relevant scripts out of {len(scripts)} total")
        return filtered_scripts

    def _calculate_script_relevance(self, script: Dict,
                                   query_analysis: DynamicQueryAnalysis) -> float:
        """Calculate how relevant a script is to the query using ALL metadata attributes"""

        score = 0.0
        content = script.get('content', '').lower()
        functions = script.get('functions', [])
        file_path = script.get('file_path', '').lower()
        query_lower = query_analysis.original_query.lower()

        # Extract rich metadata
        naming_convention = script.get('naming_convention', {})
        module = script.get('module', '').lower() if script.get('module') else ''
        app_type = script.get('app_type', '').lower() if script.get('app_type') else ''
        complexity = script.get('complexity', '').lower()
        dependencies = script.get('dependencies', [])

        # 1. NAMING CONVENTION MATCHING (High Priority)
        if naming_convention:
            # Event prefix matching (ASUA, WTUA, etc.)
            event_prefix = naming_convention.get('event_prefix', '').lower() if naming_convention.get('event_prefix') else ''
            if event_prefix and event_prefix in query_lower:
                score += 1.0

            # Module matching (SERVICEREQUEST, PERMITS, etc.)
            nc_module = naming_convention.get('module', '').lower() if naming_convention.get('module') else ''
            if nc_module:
                if nc_module in query_lower or any(word in nc_module for word in query_lower.split()):
                    score += 0.8

            # Application type matching (BUILDING, BUSINESS, etc.)
            nc_app_type = naming_convention.get('application_type', '').lower() if naming_convention.get('application_type') else ''
            if nc_app_type:
                if nc_app_type in query_lower or any(word in nc_app_type for word in query_lower.split()):
                    score += 0.7

            # Sub type and category matching
            sub_type = naming_convention.get('sub_type', '').lower() if naming_convention.get('sub_type') else ''
            category = naming_convention.get('category', '').lower() if naming_convention.get('category') else ''
            if sub_type and sub_type != 'na' and sub_type in query_lower:
                score += 0.5
            if category and category != 'na' and category in query_lower:
                score += 0.5

        # 2. MODULE AND APP_TYPE MATCHING (Legacy fields)
        if module and (module in query_lower or any(word in module for word in query_lower.split())):
            score += 0.6
        if app_type and (app_type in query_lower or any(word in app_type for word in query_lower.split())):
            score += 0.6

        # 3. FUNCTION NAME MATCHING (High value for implementation details)
        for func in functions:
            func_name = func.lower() if isinstance(func, str) else str(func).lower()
            if any(word in func_name for word in query_lower.split() if len(word) > 3):
                score += 0.4
            # Special boost for common Accela functions
            if any(accela_func in func_name for accela_func in ['createchild', 'copyaddresses', 'copycontacts', 'addcontact', 'editcap', 'workflow']):
                score += 0.3

        # 4. DEPENDENCIES MATCHING (API usage patterns)
        for dep in dependencies:
            dep_lower = dep.lower()
            if any(word in dep_lower for word in query_lower.split() if len(word) > 3):
                score += 0.3

        # 5. CONTENT MATCHING (Traditional approach)
        # Check for technical concepts
        for concept in query_analysis.entities.get('technical_concepts', []):
            if concept in content:
                score += 0.3
            if concept in file_path:
                score += 0.2

        # Check for specific terms
        for term in query_analysis.entities.get('specific_terms', []):
            if term.lower() in content:
                score += 0.4

        # Check for specific aspects
        for aspect in query_analysis.specific_aspects:
            if aspect.lower() in content:
                score += self.config.similarity_threshold * 1.5
            if aspect.lower() in file_path:
                score += self.config.similarity_threshold

        # 6. COMPLEXITY BOOST (prefer more complex scripts for detailed analysis)
        if complexity == 'high':
            score += 0.1
        elif complexity == 'medium':
            score += 0.05

        # 7. FALLBACK KEYWORD MATCHING
        if score == 0.0:
            score = self._calculate_fallback_relevance(script, query_analysis)

        return min(score, 5.0)  # Cap at 5.0 to prevent extreme scores

    def _calculate_fallback_relevance(self, script: Dict,
                                    query_analysis: DynamicQueryAnalysis) -> float:
        """Calculate relevance using keyword matching when no entities are extracted"""

        score = 0.0
        content = script.get('content', '').lower()
        file_path = script.get('file_path', '').lower()
        query_lower = query_analysis.original_query.lower()

        # Define keyword mappings for common query types
        keyword_mappings = {
            'coding standards': ['standard', 'convention', 'pattern', 'best', 'practice', 'quality'],
            'best practices': ['best', 'practice', 'optimal', 'recommended', 'standard'],
            'code quality': ['quality', 'clean', 'maintainable', 'readable', 'standard'],
            'implementation': ['implement', 'function', 'method', 'process', 'logic'],
            'workflow': ['workflow', 'process', 'step', 'approval', 'routing'],
            'error handling': ['error', 'exception', 'try', 'catch', 'handle'],
            'validation': ['validate', 'check', 'verify', 'required', 'valid'],
            'security': ['security', 'auth', 'permission', 'access', 'secure']
        }

        # Check for keyword matches
        for query_concept, keywords in keyword_mappings.items():
            if query_concept in query_lower:
                for keyword in keywords:
                    if keyword in content:
                        score += self.config.function_similarity_threshold
                    if keyword in file_path:
                        score += self.config.similarity_threshold

        # General keyword matching from query
        query_words = [word.strip() for word in query_lower.split()
                      if len(word.strip()) > 3 and word.strip() not in
                      ['which', 'county', 'following', 'better', 'accela', 'than', 'more', 'less']]

        for word in query_words:
            if word in content:
                score += self.config.similarity_threshold * 0.5
            if word in file_path:
                score += self.config.similarity_threshold * 0.3

        return min(score, 1.0)

    def _load_county_metadata(self) -> Dict[str, Dict]:
        """Load county metadata from the metadata extractor - EVENT SCRIPTS ONLY"""

        try:
            # Load metadata from JSON file instead of extracting (for performance)
            metadata_list = self.metadata_extractor.load_metadata()

            if not metadata_list:
                self.logger.warning("No metadata found, falling back to extraction")
                all_scripts = self.metadata_extractor.extract_all()
                # Convert to dict format
                metadata_list = []
                for script in all_scripts:
                    script_dict = {
                        'file_path': script.file_path,
                        'county': script.county,
                        'script_type': script.script_type,
                        'functions': script.functions,
                        'dependencies': script.dependencies,
                        'complexity': script.complexity,
                        'content': script.content or ''
                    }
                    metadata_list.append(script_dict)

            # Filter to only EVENT scripts
            event_scripts = [
                script for script in metadata_list
                if script.get('script_type') == 'event'
            ]

            self.logger.info(f"Filtered to {len(event_scripts)} event scripts from {len(metadata_list)} total scripts")

            # Group by county
            county_data = {}
            for script in event_scripts:
                county = script.get('county')
                if not county:
                    continue

                if county not in county_data:
                    county_data[county] = {'scripts': []}

                # Ensure script has content field
                if 'content' not in script:
                    script['content'] = ''

                county_data[county]['scripts'].append(script)

            self.logger.info(f"Loaded EVENT SCRIPTS for {len(county_data)} counties: {list(county_data.keys())}")
            for county, data in county_data.items():
                self.logger.info(f"County {county}: {len(data['scripts'])} event scripts")
            return county_data

        except Exception as e:
            self.logger.error(f"Failed to load county metadata: {e}")
            return {}

    def _parse_llm_county_analysis(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM county analysis response"""

        try:
            import re
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            else:
                return {}
        except json.JSONDecodeError:
            return {}

    def _parse_llm_comparison(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM comparison response"""

        try:
            import re
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            else:
                return {}
        except json.JSONDecodeError:
            return {}

    def _create_empty_comparison(self, query_analysis: DynamicQueryAnalysis) -> ComparisonResult:
        """Create empty comparison result when insufficient data"""

        return ComparisonResult(
            counties=[],
            analysis_type=query_analysis.analysis_type,
            comparison_matrix={},
            similarities=[],
            differences=[],
            best_practices=[],
            recommendations=["Insufficient data for comparison"],
            summary="Not enough county data available for comparison"
        )

    def _extract_relevant_code_sections(self, content: str, search_terms: List[str], file_path: str) -> List[Dict]:
        """Extract relevant code sections based on search terms - shows actual code"""

        sections = []
        lines = content.split('\n')

        # Look for functions and code blocks that match search terms
        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Check if line contains any search terms
            if any(term.lower() in line_lower for term in search_terms if term):
                # Extract function or code block around this line
                start_idx = max(0, i - 5)  # 5 lines before
                end_idx = min(len(lines), i + 15)  # 15 lines after

                code_block = '\n'.join(lines[start_idx:end_idx])

                # Create description based on what we found
                description = f"Code section containing '{next(term for term in search_terms if term.lower() in line_lower)}'"

                sections.append({
                    'description': description,
                    'code': code_block,
                    'file': file_path,
                    'significance': f'Found at line {i+1}'
                })

                # Limit to avoid too many similar sections
                if len(sections) >= 3:
                    break

        # If no specific matches, show the beginning of the file
        if not sections and content:
            sections.append({
                'description': f"Script content from {file_path}",
                'code': '\n'.join(lines[:20]),  # First 20 lines
                'file': file_path,
                'significance': 'Beginning of script'
            })

        return sections

    def _compare_with_knowledge_base(self, query_analysis: DynamicQueryAnalysis,
                                   county_results: List[CountyAnalysisResult]) -> ComparisonResult:
        """Compare counties using knowledge base only - no LLM manipulation"""

        if len(county_results) < 2:
            self.logger.warning("Need at least 2 counties for comparison")
            return self._create_empty_comparison(query_analysis)

        counties = [r.county for r in county_results]

        # Extract similarities and differences from actual data
        similarities = []
        differences = []
        best_practices = []

        # Compare code examples
        all_code_examples = []
        for result in county_results:
            all_code_examples.extend(result.code_examples)

        # Find common patterns in code
        common_functions = set()
        for example in all_code_examples:
            code = example.get('code', '')
            # Extract function names
            import re
            functions = re.findall(r'function\s+(\w+)', code)
            common_functions.update(functions)

        if common_functions:
            similarities.append(f"Common functions used: {', '.join(list(common_functions)[:5])}")

        # Compare findings
        for i, result1 in enumerate(county_results):
            for result2 in county_results[i+1:]:
                # Compare strengths
                common_strengths = set(result1.strengths) & set(result2.strengths)
                if common_strengths:
                    similarities.extend([f"Both {result1.county} and {result2.county}: {strength}"
                                       for strength in common_strengths])

                # Find differences
                unique_strengths1 = set(result1.strengths) - set(result2.strengths)
                unique_strengths2 = set(result2.strengths) - set(result1.strengths)

                for strength in unique_strengths1:
                    differences.append({
                        'aspect': 'Implementation Strength',
                        'county1': result1.county,
                        'county1_approach': strength,
                        'county2': result2.county,
                        'county2_approach': 'Not implemented'
                    })

        # Extract best practices from strengths
        for result in county_results:
            for strength in result.strengths:
                best_practices.append({
                    'practice': strength,
                    'county': result.county,
                    'description': f"Implemented in {result.county}"
                })

        return ComparisonResult(
            counties=counties,
            analysis_type=query_analysis.analysis_type,
            comparison_matrix={county: {'analyzed': True} for county in counties},
            similarities=similarities[:5],  # Top 5
            differences=differences[:5],    # Top 5
            best_practices=best_practices[:5],  # Top 5
            recommendations=[f"Review implementation differences between {counties[0]} and {counties[1]}"],
            summary=f"Knowledge-base comparison of {len(counties)} counties"
        )
